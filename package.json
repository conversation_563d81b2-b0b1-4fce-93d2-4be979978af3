{"name": "telesoft-ui", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:intsoc": "turbo run dev --filter @telesoft/intsoc-* watch --filter ./packages/* --parallel", "build:intsoc": "turbo run build --filter @telesoft/intsoc-*", "package:intsoc-frontend": "cd apps/frontend/intsoc && pnpm run package", "package:intsoc-backend": "cd apps/backend/intsoc && pnpm run package", "build-and-package:intsoc-frontend": "turbo run build --filter @telesoft/intsoc-frontend && turbo run package --filter @telesoft/intsoc-frontend", "build-and-package:intsoc-backend": "turbo run build --filter @telesoft/intsoc-backend && turbo run package --filter @telesoft/intsoc-backend", "start:intsoc": "turbo run start --filter @telesoft/intsoc-*", "start:intsoc-frontend": "cd apps/frontend/intsoc && pnpm build && pnpm start", "start:intsoc-backend": "cd apps/backend/intsoc && pnpm build && pnpm start", "prod:intsoc": "pnpm run build:intsoc && pnpm run start:intsoc", "docs": "cd documentation && pnpm dev", "docs:build": "cd documentation && pnpm build", "watch": "turbo run watch", "watch:packages": "turbo run watch --filter='./packages/*'", "watch:apps": "turbo run watch --filter='./apps/*'", "lint": "turbo run format && turbo run lint --concurrency=1", "test": "turbo run test", "clean": "turbo run clean", "type-check": "turbo run type-check", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "build:packages": "turbo run build --filter='./packages/*'", "build:apps": "turbo run build --filter='./apps/*'", "test:watch": "turbo run test --watch", "test:coverage": "turbo run test -- --coverage", "lint:fix": "turbo run lint -- --fix", "typecheck": "turbo run type-check", "validate": "turbo run validate", "check-updates": "pnpm -r outdated", "check-catalog": "pnpm ls --depth=-1 | grep catalog", "graph": "turbo run build --graph", "clean:all": "turbo run clean && rm -rf node_modules **/node_modules", "install:clean": "pnpm run clean:all && pnpm install", "preinstall": "npx only-allow pnpm", "prepare": "husky install || true"}, "packageManager": "pnpm@10.11.1", "engines": {"node": ">=24", "pnpm": ">=10"}, "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "jsonwebtoken": "^9.0.2", "next": "catalog:next-15", "qrcode": "^1.5.4", "react": "catalog:next-15", "react-dom": "catalog:next-15", "speakeasy": "^2.0.0"}, "devDependencies": {"prettier": "3.5.3", "turbo": "catalog:build"}}